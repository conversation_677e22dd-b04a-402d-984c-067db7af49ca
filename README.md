# صفحة هبوط احترافية - Landing Page

صفحة هبوط حديثة ومتجاوبة مبنية بـ HTML5, CSS3, Bootstrap 5, وJavaScript مع دعم كامل للغة العربية (RTL) وإمكانية التبديل للإنجليزية (LTR).

## 🚀 المميزات

- **تصميم متجاوب بالكامل**: يعمل بشكل مثالي على جميع الأجهزة (Desktop, Tablet, Mobile)
- **دعم RTL/LTR**: تبديل سلس بين العربية والإنجليزية
- **أداء محسّن**: تحميل سريع مع lazy loading للصور
- **SEO محسّن**: عناوين وmeta tags صحيحة
- **إمكانية الوصول (a11y)**: متوافق مع معايير الوصولية
- **حركات أنيقة**: تأثيرات بصرية جذابة
- **نماذج تفاعلية**: تحقق من صحة البيانات مع رسائل تنبيه

## 📁 هيكل المشروع

```
├── index.html              # الصفحة الرئيسية
├── assets/
│   ├── css/
│   │   └── style.css       # ملف الأنماط الرئيسي
│   ├── js/
│   │   └── main.js         # ملف JavaScript الرئيسي
│   └── img/
│       ├── logo.svg        # شعار الشركة
│       ├── logo-white.svg  # شعار أبيض للخلفيات الداكنة
│       ├── hero-illustration.svg  # رسم توضيحي للقسم الرئيسي
│       ├── testimonial-1.jpg      # صورة العميل الأول
│       ├── testimonial-2.jpg      # صورة العميل الثاني
│       └── testimonial-3.jpg      # صورة العميل الثالث
└── README.md               # هذا الملف
```

## 🎨 تخصيص الألوان

يمكنك تغيير ألوان الموقع بسهولة عبر تعديل متغيرات CSS في ملف `assets/css/style.css`:

```css
:root {
  /* الألوان الأساسية */
  --primary-color: #2563eb;      /* اللون الأساسي */
  --primary-dark: #1d4ed8;       /* اللون الأساسي الداكن */
  --primary-light: #3b82f6;      /* اللون الأساسي الفاتح */
  
  /* الألوان الثانوية */
  --secondary-color: #64748b;
  --secondary-dark: #475569;
  --secondary-light: #94a3b8;
  
  /* ألوان التمييز */
  --accent-color: #f59e0b;       /* لون التمييز */
  --accent-dark: #d97706;
  --accent-light: #fbbf24;
}
```

### أمثلة على تغيير الألوان:

**للحصول على مظهر أخضر:**
```css
--primary-color: #10b981;
--primary-dark: #059669;
--primary-light: #34d399;
```

**للحصول على مظهر بنفسجي:**
```css
--primary-color: #8b5cf6;
--primary-dark: #7c3aed;
--primary-light: #a78bfa;
```

## ✏️ تخصيص النصوص

### تغيير النصوص الأساسية:

1. **العنوان الرئيسي**: ابحث عن `.hero-title` في `index.html`
2. **الوصف**: ابحث عن `.hero-subtitle` في `index.html`
3. **أزرار الدعوة للعمل**: ابحث عن `.cta-btn` في `index.html`

### تغيير معلومات الشركة:

في ملف `index.html`، ابحث عن الأقسام التالية وقم بتعديلها:

```html
<!-- معلومات الشركة في الـ Footer -->
<div class="col-lg-4 mb-4">
    <img src="assets/img/logo-white.svg" alt="الشعار" height="40" class="mb-3">
    <p class="footer-description">نقدم حلولاً تقنية مبتكرة...</p>
</div>
```

## 🔧 تخصيص الخطوط

الموقع يستخدم خطوط Google Fonts. لتغيير الخطوط:

1. **للعربية**: عدّل `--font-family-arabic` في CSS
2. **للإنجليزية**: عدّل `--font-family-english` في CSS

```css
:root {
  --font-family-arabic: 'Cairo', 'Segoe UI', sans-serif;
  --font-family-english: 'Poppins', 'Segoe UI', sans-serif;
}
```

## 📱 الاستجابة (Responsive)

الموقع محسّن للعمل على:
- **Desktop**: 1920px وأكبر
- **Laptop**: 1024px - 1919px
- **Tablet**: 768px - 1023px
- **Mobile**: 320px - 767px

## 🚀 كيفية التشغيل

### التشغيل المحلي:

1. **تحميل الملفات**: انسخ جميع الملفات إلى مجلد على جهازك
2. **فتح الملف**: افتح `index.html` في المتصفح مباشرة

### التشغيل على خادم محلي:

```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js (npx)
npx serve .

# باستخدام PHP
php -S localhost:8000
```

ثم افتح `http://localhost:8000` في المتصفح.

## 🔗 تخصيص الروابط والنماذج

### تغيير endpoint النماذج:

في ملف `assets/js/main.js`، ابحث عن:

```javascript
// تغيير رابط إرسال البيانات
sendToAPI('/api/lead', { email: email })     // للتسجيل
sendToAPI('/api/contact', data)              // لنموذج الاتصال
```

### إضافة روابط التواصل الاجتماعي:

في `index.html`، ابحث عن `.social-links` وعدّل الروابط:

```html
<div class="social-links">
    <a href="https://facebook.com/yourpage" class="social-link">
        <i class="fab fa-facebook"></i>
    </a>
    <!-- أضف المزيد من الروابط -->
</div>
```

## 🎯 تحسين الأداء

### نصائح لتحسين الأداء:

1. **ضغط الصور**: استخدم تنسيقات WebP أو AVIF
2. **تصغير CSS/JS**: استخدم أدوات التصغير للإنتاج
3. **CDN**: استخدم CDN لتحميل Bootstrap وFont Awesome
4. **Lazy Loading**: مفعّل افتراضياً للصور

### تصغير الملفات للإنتاج:

```bash
# تصغير CSS
npx clean-css-cli assets/css/style.css -o assets/css/style.min.css

# تصغير JavaScript
npx terser assets/js/main.js -o assets/js/main.min.js
```

## 🔍 تحسين محركات البحث (SEO)

### Meta Tags المهمة:

```html
<meta name="description" content="وصف موقعك هنا">
<meta name="keywords" content="كلمات مفتاحية, مفصولة, بفواصل">
<meta property="og:title" content="عنوان الصفحة">
<meta property="og:description" content="وصف للمشاركة">
<meta property="og:image" content="رابط صورة المشاركة">
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **الخطوط لا تظهر**: تأكد من اتصال الإنترنت لتحميل Google Fonts
2. **الأيقونات لا تظهر**: تأكد من تحميل Font Awesome
3. **النماذج لا تعمل**: تحقق من console المتصفح للأخطاء

### فحص الأداء:

استخدم أدوات المطور في المتصفح:
- **Lighthouse**: لفحص الأداء والـ SEO
- **Network Tab**: لفحص سرعة التحميل
- **Console**: لفحص أخطاء JavaScript

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:

1. تحقق من ملف README هذا
2. افحص console المتصفح للأخطاء
3. تأكد من أن جميع الملفات في مكانها الصحيح

## 📄 الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

---

**ملاحظة**: تذكر تغيير جميع النصوص والصور والروابط لتناسب شركتك قبل النشر!
