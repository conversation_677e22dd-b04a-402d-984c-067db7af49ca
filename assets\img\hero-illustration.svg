<svg width="500" height="400" viewBox="0 0 500 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heroGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heroGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#34d399;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Elements -->
  <circle cx="100" cy="100" r="60" fill="url(#heroGradient1)" opacity="0.1"/>
  <circle cx="400" cy="300" r="80" fill="url(#heroGradient2)" opacity="0.1"/>
  <circle cx="350" cy="80" r="40" fill="url(#heroGradient3)" opacity="0.1"/>
  
  <!-- Main Illustration -->
  <!-- Computer/Device -->
  <rect x="150" y="150" width="200" height="140" rx="10" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
  <rect x="160" y="160" width="180" height="100" rx="5" fill="url(#heroGradient1)"/>
  
  <!-- Screen Content -->
  <rect x="170" y="170" width="160" height="6" rx="3" fill="#ffffff" opacity="0.8"/>
  <rect x="170" y="185" width="120" height="4" rx="2" fill="#ffffff" opacity="0.6"/>
  <rect x="170" y="195" width="140" height="4" rx="2" fill="#ffffff" opacity="0.6"/>
  
  <!-- Charts/Graphs -->
  <rect x="170" y="210" width="30" height="20" rx="2" fill="url(#heroGradient2)"/>
  <rect x="210" y="215" width="30" height="15" rx="2" fill="url(#heroGradient3)"/>
  <rect x="250" y="205" width="30" height="25" rx="2" fill="url(#heroGradient1)"/>
  <rect x="290" y="220" width="30" height="10" rx="2" fill="url(#heroGradient2)"/>
  
  <!-- Floating Elements -->
  <!-- Success Icon -->
  <circle cx="380" cy="120" r="25" fill="url(#heroGradient3)"/>
  <path d="M370 120 L378 128 L390 112" stroke="#ffffff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Notification -->
  <rect x="80" y="200" width="60" height="40" rx="8" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
  <circle cx="95" cy="215" r="4" fill="url(#heroGradient2)"/>
  <rect x="105" y="210" width="25" height="3" rx="1" fill="#cbd5e1"/>
  <rect x="105" y="218" width="20" height="2" rx="1" fill="#e2e8f0"/>
  <rect x="105" y="225" width="15" height="2" rx="1" fill="#e2e8f0"/>
  
  <!-- Growth Arrow -->
  <path d="M320 80 L380 40" stroke="url(#heroGradient3)" stroke-width="4" stroke-linecap="round"/>
  <path d="M370 35 L380 40 L375 50" stroke="url(#heroGradient3)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Data Points -->
  <circle cx="120" cy="320" r="8" fill="url(#heroGradient1)"/>
  <circle cx="180" cy="310" r="6" fill="url(#heroGradient2)"/>
  <circle cx="240" cy="330" r="7" fill="url(#heroGradient3)"/>
  <circle cx="300" cy="315" r="5" fill="url(#heroGradient1)"/>
  
  <!-- Connection Lines -->
  <path d="M120 320 Q150 300 180 310" stroke="#cbd5e1" stroke-width="2" fill="none"/>
  <path d="M180 310 Q210 320 240 330" stroke="#cbd5e1" stroke-width="2" fill="none"/>
  <path d="M240 330 Q270 320 300 315" stroke="#cbd5e1" stroke-width="2" fill="none"/>
  
  <!-- Decorative Elements -->
  <rect x="50" y="50" width="8" height="8" rx="2" fill="url(#heroGradient2)" opacity="0.6"/>
  <rect x="420" y="200" width="6" height="6" rx="1" fill="url(#heroGradient1)" opacity="0.6"/>
  <rect x="450" y="250" width="10" height="10" rx="2" fill="url(#heroGradient3)" opacity="0.6"/>
  
  <!-- Cloud Elements -->
  <ellipse cx="100" cy="60" rx="15" ry="8" fill="#ffffff" stroke="#e2e8f0"/>
  <ellipse cx="110" cy="55" rx="12" ry="6" fill="#ffffff" stroke="#e2e8f0"/>
  <ellipse cx="90" cy="55" rx="10" ry="5" fill="#ffffff" stroke="#e2e8f0"/>
  
  <ellipse cx="420" cy="350" rx="18" ry="10" fill="#ffffff" stroke="#e2e8f0"/>
  <ellipse cx="435" cy="345" rx="15" ry="8" fill="#ffffff" stroke="#e2e8f0"/>
  <ellipse cx="405" cy="345" rx="12" ry="6" fill="#ffffff" stroke="#e2e8f0"/>
</svg>
