/* ===== CSS Variables for Theme Customization ===== */
:root {
  /* Primary Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  
  /* Secondary Colors */
  --secondary-color: #64748b;
  --secondary-dark: #475569;
  --secondary-light: #94a3b8;
  
  /* Accent Colors */
  --accent-color: #f59e0b;
  --accent-dark: #d97706;
  --accent-light: #fbbf24;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Success, Warning, Error */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  /* Typography */
  --font-family-arabic: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-english: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  
  /* Spacing */
  --section-padding: 5rem 0;
  --container-padding: 1rem;
  
  /* Border Radius */
  --border-radius-sm: 0.375rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== RTL/LTR Support ===== */
[dir="rtl"] {
  --font-family-primary: var(--font-family-arabic);
}

[dir="ltr"] {
  --font-family-primary: var(--font-family-english);
}

/* ===== Base Styles ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary, var(--font-family-arabic));
  line-height: 1.6;
  color: var(--gray-700);
  background-color: var(--white);
  overflow-x: hidden;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--gray-900);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--gray-600);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

/* ===== Language Toggle ===== */
.language-toggle {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1050;
}

[dir="rtl"] .language-toggle {
  left: auto;
  right: 20px;
}

/* ===== Header Styles ===== */
.navbar {
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95) !important;
}

.navbar-brand .logo {
  transition: transform var(--transition-fast);
}

.navbar-brand:hover .logo {
  transform: scale(1.05);
}

.nav-link {
  font-weight: 500;
  color: var(--gray-700) !important;
  transition: color var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color) !important;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: all var(--transition-fast);
  transform: translateX(-50%);
}

.nav-link:hover::after {
  width: 100%;
}

.cta-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-lg);
  font-weight: 600;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow);
}

.cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* ===== Hero Section ===== */
.hero-section {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%232563eb" stop-opacity="0.1"/><stop offset="100%" stop-color="%232563eb" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="300" fill="url(%23a)"/><circle cx="800" cy="800" r="400" fill="url(%23a)"/></svg>');
  background-size: cover;
  opacity: 0.5;
  z-index: -1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--gray-900), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--gray-600);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.email-capture .form-control {
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  transition: all var(--transition-fast);
}

.email-capture .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.email-capture .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--border-radius-lg);
  white-space: nowrap;
}

.trust-indicators {
  margin-top: 3rem;
}

.trust-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.trust-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
}

.trust-label {
  font-size: 0.875rem;
  color: var(--gray-500);
  display: block;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(var(--shadow-xl));
}

/* ===== Section Styles ===== */
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  margin-bottom: 3rem;
}

/* ===== Features Section ===== */
.features-section {
  padding: var(--section-padding);
}

.feature-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  text-align: center;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: all var(--transition-normal);
}

.feature-icon i {
  font-size: 2rem;
  color: var(--white);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-link {
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.feature-link:hover {
  color: var(--primary-dark);
  transform: translateX(-5px);
}

[dir="rtl"] .feature-link:hover {
  transform: translateX(5px);
}

/* ===== How it Works Section ===== */
.how-it-works-section {
  background-color: var(--gray-50);
  padding: var(--section-padding);
}

.step-item {
  position: relative;
  padding: 2rem 1rem;
}

.step-number {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
  z-index: 2;
}

.step-icon {
  width: 100px;
  height: 100px;
  background: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem auto 1.5rem;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
}

.step-icon i {
  font-size: 2.5rem;
  color: var(--primary-color);
}

.step-item:hover .step-icon {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.step-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.step-description {
  color: var(--gray-600);
  line-height: 1.6;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .email-capture .d-flex {
    flex-direction: column !important;
  }
  
  .trust-indicators .d-flex {
    flex-direction: column !important;
    gap: 1rem !important;
  }
  
  .feature-card {
    margin-bottom: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
  
  .feature-card,
  .step-item {
    padding: 1.5rem;
  }
}

/* ===== Animation Classes ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--transition-slow);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* ===== Utility Classes ===== */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border: none;
  transition: all var(--transition-fast);
}

.btn-gradient:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== Pricing Section ===== */
.pricing-section {
  padding: var(--section-padding);
  background-color: var(--white);
}

.pricing-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all var(--transition-normal);
  border: 2px solid var(--gray-200);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.pricing-card.recommended {
  border-color: var(--primary-color);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.pricing-card.recommended:hover {
  transform: scale(1.05) translateY(-10px);
}

.recommended-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
  color: var(--white);
  padding: 0.5rem 1.5rem;
  border-radius: var(--border-radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
}

.pricing-header {
  margin-bottom: 2rem;
}

.pricing-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

.pricing-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.pricing-price .currency {
  font-size: 1.25rem;
  color: var(--gray-500);
}

.pricing-price .amount {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
}

.pricing-price .period {
  font-size: 1rem;
  color: var(--gray-500);
}

.pricing-features {
  flex-grow: 1;
  margin-bottom: 2rem;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricing-features li {
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--gray-100);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.pricing-features li:last-child {
  border-bottom: none;
}

.pricing-features i {
  color: var(--success-color);
  font-size: 1rem;
  flex-shrink: 0;
}

.pricing-card .btn {
  margin-top: auto;
  padding: 1rem 2rem;
  font-weight: 600;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
}

/* ===== Testimonials Section ===== */
.testimonials-section {
  padding: var(--section-padding);
  background-color: var(--gray-50);
}

.testimonial-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.testimonial-content {
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

.testimonial-stars {
  margin-bottom: 1rem;
}

.testimonial-stars i {
  color: var(--accent-color);
  font-size: 1.125rem;
  margin-right: 0.25rem;
}

[dir="rtl"] .testimonial-stars i {
  margin-right: 0;
  margin-left: 0.25rem;
}

.testimonial-text {
  font-style: italic;
  color: var(--gray-600);
  line-height: 1.6;
  font-size: 1.125rem;
  margin-bottom: 0;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
}

.author-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.author-title {
  font-size: 0.875rem;
  color: var(--gray-500);
  margin-bottom: 0;
}

/* ===== FAQ Section ===== */
.faq-section {
  padding: var(--section-padding);
  background-color: var(--white);
}

.accordion-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  margin-bottom: 1rem;
  overflow: hidden;
}

.accordion-item:last-child {
  margin-bottom: 0;
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-button {
  background-color: var(--white);
  border: none;
  padding: 1.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-900);
  text-align: right;
  transition: all var(--transition-fast);
}

[dir="ltr"] .accordion-button {
  text-align: left;
}

.accordion-button:not(.collapsed) {
  background-color: var(--primary-color);
  color: var(--white);
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
  border-color: var(--primary-color);
}

.accordion-button::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23374151'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  transition: transform var(--transition-fast);
}

.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  transform: rotate(180deg);
}

.accordion-body {
  padding: 1.5rem;
  background-color: var(--gray-50);
  color: var(--gray-600);
  line-height: 1.6;
  border-top: 1px solid var(--gray-200);
}

/* ===== Contact Section ===== */
.contact-section {
  padding: var(--section-padding);
  background-color: var(--gray-50);
}

.contact-form {
  background: var(--white);
  padding: 3rem;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
}

.contact-form .form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.contact-form .form-control {
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1rem;
  font-size: 1rem;
  transition: all var(--transition-fast);
}

.contact-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.contact-form .form-control.is-invalid {
  border-color: var(--error-color);
}

.contact-form .invalid-feedback {
  color: var(--error-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* ===== CTA Section ===== */
.cta-section {
  padding: var(--section-padding);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  text-align: center;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 1rem;
}

.cta-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-buttons .btn {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
}

.cta-buttons .btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.5);
  color: var(--white);
}

.cta-buttons .btn-outline-light:hover {
  background-color: var(--white);
  color: var(--primary-color);
  border-color: var(--white);
  transform: translateY(-2px);
}

/* ===== Footer ===== */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-300);
  padding: 3rem 0 1rem;
}

.footer-description {
  color: var(--gray-400);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background-color: var(--gray-800);
  color: var(--gray-400);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.social-link:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.footer-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-divider {
  border-color: var(--gray-700);
  margin: 2rem 0 1rem;
}

.copyright,
.footer-note {
  color: var(--gray-500);
  font-size: 0.875rem;
  margin-bottom: 0;
}

.footer-note i {
  color: var(--error-color);
  margin: 0 0.25rem;
}

/* ===== Additional Responsive Improvements ===== */
@media (max-width: 992px) {
  .pricing-card.recommended {
    transform: none;
    margin-bottom: 2rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 0;
  }

  .hero-section .min-vh-100 {
    min-height: auto;
  }

  .section-title {
    font-size: 1.875rem;
  }

  .contact-form {
    padding: 2rem;
  }

  .testimonial-card {
    margin-bottom: 1.5rem;
  }

  .step-item {
    padding: 1.5rem 0.5rem;
  }

  .navbar-brand .logo {
    height: 35px;
  }
}

@media (max-width: 576px) {
  .language-toggle {
    top: 10px;
    left: 10px;
  }

  [dir="rtl"] .language-toggle {
    left: auto;
    right: 10px;
  }

  .hero-title {
    font-size: 1.75rem;
    line-height: 1.3;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .feature-card,
  .pricing-card,
  .testimonial-card {
    padding: 1.5rem;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .cta-title {
    font-size: 1.875rem;
  }

  .cta-subtitle {
    font-size: 1rem;
  }

  .trust-indicators .d-flex {
    gap: 1.5rem !important;
  }

  .trust-number {
    font-size: 1.25rem;
  }

  .trust-label {
    font-size: 0.75rem;
  }
}

/* ===== Print Styles ===== */
@media print {
  .language-toggle,
  .navbar,
  .cta-section,
  .contact-section {
    display: none !important;
  }

  .hero-section,
  .features-section,
  .pricing-section {
    page-break-inside: avoid;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .section-title {
    font-size: 18pt;
  }
}

/* ===== Dark Mode Support (Optional) ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #1e293b;
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-900: #f8fafc;
  }

  .navbar {
    background-color: rgba(15, 23, 42, 0.95) !important;
  }

  .hero-section::before {
    opacity: 0.3;
  }
}

/* ===== Accessibility Improvements ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .fade-in {
    opacity: 1;
    transform: none;
  }
}

/* ===== Focus Styles for Better Accessibility ===== */
.btn:focus,
.form-control:focus,
.nav-link:focus,
.accordion-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== High Contrast Mode Support ===== */
@media (prefers-contrast: high) {
  .feature-card,
  .pricing-card,
  .testimonial-card {
    border-width: 2px;
    border-color: var(--gray-900);
  }

  .btn {
    border-width: 2px;
  }
}

/* ===== Loading States ===== */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== Notification Styles ===== */
.notification {
  font-family: var(--font-family-primary);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: auto;
}

[dir="rtl"] .notification-close {
  margin-left: 0;
  margin-right: auto;
}

/* ===== Scroll to Top Button (Optional) ===== */
.scroll-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 1000;
}

[dir="rtl"] .scroll-to-top {
  right: auto;
  left: 20px;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.scroll-to-top:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

/* ===== Keyboard Navigation Styles ===== */
.keyboard-navigation *:focus {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

.skip-link:focus {
  top: 6px !important;
}

/* ===== Cookie Banner Styles ===== */
.cookie-banner {
  font-family: var(--font-family-primary);
}

.cookie-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  gap: 1rem;
}

.cookie-buttons {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .cookie-content {
    flex-direction: column;
    text-align: center;
  }

  .cookie-buttons {
    width: 100%;
    justify-content: center;
  }
}

/* ===== Enhanced Form Validation Styles ===== */
.form-control.is-valid {
  border-color: var(--success-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

[dir="rtl"] .form-control.is-valid {
  background-position: left 0.75rem center;
}

.form-control.is-invalid {
  border-color: var(--error-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4M8.2 4.6l-2.4 2.4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

[dir="rtl"] .form-control.is-invalid {
  background-position: left 0.75rem center;
}

/* ===== Loading Animation for Buttons ===== */
.btn.loading {
  position: relative;
  color: transparent !important;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  color: inherit;
}

/* ===== Smooth Scrolling Enhancement ===== */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* ===== Enhanced Hover Effects ===== */
@media (hover: hover) {
  .feature-card:hover .feature-icon {
    animation: bounce 0.6s ease-in-out;
  }

  .pricing-card:hover {
    animation: pulse 0.6s ease-in-out;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0) scale(1.1);
  }
  40%, 43% {
    transform: translate3d(0,-10px,0) scale(1.1);
  }
  70% {
    transform: translate3d(0,-5px,0) scale(1.1);
  }
  90% {
    transform: translate3d(0,-2px,0) scale(1.1);
  }
}

@keyframes pulse {
  0% {
    transform: translateY(-10px) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.02);
  }
  100% {
    transform: translateY(-10px) scale(1);
  }
}

/* ===== Error States ===== */
.error-state {
  color: var(--error-color);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--error-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
}

.success-state {
  color: var(--success-color);
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid var(--success-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
}

/* ===== Final Responsive Adjustments ===== */
@media (max-width: 480px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .hero-section .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .btn {
    font-size: 0.9rem;
    padding: 0.75rem 1.25rem;
  }

  .btn-lg {
    font-size: 1rem;
    padding: 0.875rem 1.5rem;
  }
}

/* ===== Print Optimization ===== */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .btn {
    border: 1px solid black;
    background: transparent;
    color: black;
  }

  .hero-section::before {
    display: none;
  }
}
