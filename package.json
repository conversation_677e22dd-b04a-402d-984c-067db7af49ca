{"name": "professional-landing-page", "version": "1.0.0", "description": "صفحة هبوط احترافية مبنية بـ HTML5, CSS3, Bootstrap 5, وJavaScript مع دعم RTL/LTR", "main": "index.html", "scripts": {"start": "npx serve . -p 3000", "dev": "npx live-server --port=3000 --open=/", "build": "npm run minify-css && npm run minify-js", "minify-css": "npx clean-css-cli assets/css/style.css -o assets/css/style.min.css", "minify-js": "npx terser assets/js/main.js -o assets/js/main.min.js --compress --mangle", "optimize-images": "npx imagemin assets/img/* --out-dir=assets/img/optimized", "lighthouse": "npx lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "test": "echo \"No tests specified\" && exit 0", "validate-html": "npx html-validate index.html", "validate-css": "npx stylelint assets/css/style.css", "format": "npx prettier --write *.html assets/**/*.{css,js}", "zip": "npx bestzip landing-page.zip index.html assets/ README.md package.json"}, "keywords": ["landing-page", "html5", "css3", "bootstrap5", "javascript", "rtl", "arabic", "responsive", "professional"], "author": "Your Company", "license": "MIT", "devDependencies": {"clean-css-cli": "^5.6.2", "terser": "^5.19.4", "imagemin-cli": "^7.0.0", "lighthouse": "^11.0.0", "html-validate": "^8.5.0", "stylelint": "^15.10.3", "prettier": "^3.0.3", "bestzip": "^2.2.1", "live-server": "^1.2.2", "serve": "^14.2.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "repository": {"type": "git", "url": "https://github.com/yourcompany/landing-page.git"}, "bugs": {"url": "https://github.com/yourcompany/landing-page/issues"}, "homepage": "https://yourcompany.github.io/landing-page"}