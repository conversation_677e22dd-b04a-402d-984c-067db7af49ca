<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="testimonialGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="100" height="100" rx="50" fill="url(#testimonialGradient2)"/>
  
  <!-- Person Icon -->
  <circle cx="50" cy="35" r="15" fill="#ffffff" opacity="0.9"/>
  <path d="M25 75 Q50 60 75 75 L75 85 Q50 70 25 85 Z" fill="#ffffff" opacity="0.9"/>
</svg>
