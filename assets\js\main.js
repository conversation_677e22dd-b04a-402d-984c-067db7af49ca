/**
 * Main JavaScript file for the landing page
 * Features: RTL/LTR toggle, smooth scrolling, form validation, animations
 */

// ===== Global Variables =====
let currentLanguage = 'ar';
const translations = {
  ar: {
    lang: 'ar',
    dir: 'rtl',
    toggleText: 'EN',
    title: 'حلول مبتكرة لنجاح أعمالك',
    subtitle: 'نقدم لك أفضل الخدمات التقنية المتطورة لتحقيق أهدافك بكفاءة عالية',
    cta: 'احصل عليه الآن',
    startFree: 'ابدأ مجاناً',
    emailPlaceholder: 'أدخل بريدك الإلكتروني'
  },
  en: {
    lang: 'en',
    dir: 'ltr',
    toggleText: 'AR',
    title: 'Innovative Solutions for Your Business Success',
    subtitle: 'We provide you with the best advanced technical services to achieve your goals efficiently',
    cta: 'Get It Now',
    startFree: 'Start Free',
    emailPlaceholder: 'Enter your email address'
  }
};

// ===== DOM Content Loaded =====
document.addEventListener('DOMContentLoaded', function() {
  initializeApp();
});

// ===== Initialize Application =====
function initializeApp() {
  setupLanguageToggle();
  setupSmoothScrolling();
  setupFormValidation();
  setupAnimations();
  setupTooltips();
  setupNavbarScroll();
  setupLazyLoading();
}

// ===== Language Toggle Functionality =====
function setupLanguageToggle() {
  const langToggle = document.getElementById('langToggle');
  
  if (langToggle) {
    langToggle.addEventListener('click', function() {
      toggleLanguage();
    });
  }
}

function toggleLanguage() {
  currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
  const translation = translations[currentLanguage];
  
  // Update HTML attributes
  document.documentElement.lang = translation.lang;
  document.documentElement.dir = translation.dir;
  
  // Update toggle button text
  const langToggle = document.getElementById('langToggle');
  if (langToggle) {
    langToggle.innerHTML = `<i class="fas fa-globe"></i> ${translation.toggleText}`;
  }
  
  // Update main content
  updateContent(translation);
  
  // Update CSS variables for font family
  document.documentElement.style.setProperty(
    '--font-family-primary', 
    translation.lang === 'ar' ? 'var(--font-family-arabic)' : 'var(--font-family-english)'
  );
  
  // Trigger animations again
  setupAnimations();
}

function updateContent(translation) {
  // Update hero section
  const heroTitle = document.querySelector('.hero-title');
  const heroSubtitle = document.querySelector('.hero-subtitle');
  const ctaBtn = document.querySelector('.cta-btn');
  const startFreeBtn = document.querySelector('.email-capture .btn');
  const emailInput = document.querySelector('.email-capture input[type="email"]');
  
  if (heroTitle) heroTitle.textContent = translation.title;
  if (heroSubtitle) heroSubtitle.textContent = translation.subtitle;
  if (ctaBtn) ctaBtn.textContent = translation.cta;
  if (startFreeBtn) startFreeBtn.textContent = translation.startFree;
  if (emailInput) emailInput.placeholder = translation.emailPlaceholder;
}

// ===== Smooth Scrolling =====
function setupSmoothScrolling() {
  const navLinks = document.querySelectorAll('a[href^="#"]');
  
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      
      const targetId = this.getAttribute('href');
      const targetSection = document.querySelector(targetId);
      
      if (targetSection) {
        const headerHeight = document.querySelector('.navbar').offsetHeight;
        const targetPosition = targetSection.offsetTop - headerHeight;
        
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });
}

// ===== Form Validation =====
function setupFormValidation() {
  // Email capture form
  const emailForm = document.getElementById('emailForm');
  if (emailForm) {
    emailForm.addEventListener('submit', function(e) {
      e.preventDefault();
      handleEmailSubmission(this);
    });
  }
  
  // Contact form
  const contactForm = document.getElementById('contactForm');
  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      handleContactSubmission(this);
    });
  }
}

function handleEmailSubmission(form) {
  const emailInput = form.querySelector('input[type="email"]');
  const email = emailInput.value.trim();
  
  if (!isValidEmail(email)) {
    showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
    return;
  }
  
  // Simulate API call
  const submitBtn = form.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  
  submitBtn.disabled = true;
  submitBtn.textContent = 'جاري الإرسال...';
  
  setTimeout(() => {
    // Simulate successful submission
    sendToAPI('/api/lead', { email: email })
      .then(() => {
        showNotification('تم التسجيل بنجاح! سنتواصل معك قريباً', 'success');
        emailInput.value = '';
      })
      .catch(() => {
        showNotification('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
      })
      .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
      });
  }, 1000);
}

function handleContactSubmission(form) {
  const formData = new FormData(form);
  const data = {
    name: formData.get('name'),
    email: formData.get('email'),
    message: formData.get('message')
  };
  
  // Validate form data
  if (!data.name || !data.email || !data.message) {
    showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
    return;
  }
  
  if (!isValidEmail(data.email)) {
    showNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
    return;
  }
  
  // Simulate API call
  const submitBtn = form.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  
  submitBtn.disabled = true;
  submitBtn.textContent = 'جاري الإرسال...';
  
  setTimeout(() => {
    sendToAPI('/api/contact', data)
      .then(() => {
        showNotification('تم إرسال رسالتك بنجاح! سنرد عليك قريباً', 'success');
        form.reset();
      })
      .catch(() => {
        showNotification('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
      })
      .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
      });
  }, 1000);
}

// ===== API Simulation =====
function sendToAPI(endpoint, data) {
  return new Promise((resolve, reject) => {
    // Simulate API call
    console.log(`Sending to ${endpoint}:`, data);
    
    // Simulate random success/failure for demo
    const success = Math.random() > 0.1; // 90% success rate
    
    setTimeout(() => {
      if (success) {
        resolve({ success: true, message: 'Data sent successfully' });
      } else {
        reject(new Error('API Error'));
      }
    }, 500);
  });
}

// ===== Utility Functions =====
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function showNotification(message, type = 'info') {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <i class="fas fa-${getNotificationIcon(type)}"></i>
      <span>${message}</span>
      <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
        <i class="fas fa-times"></i>
      </button>
    </div>
  `;
  
  // Add styles
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    background: ${getNotificationColor(type)};
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
    word-wrap: break-word;
  `;
  
  // Add to DOM
  document.body.appendChild(notification);
  
  // Animate in
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);
  
  // Auto remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove();
        }
      }, 300);
    }
  }, 5000);
}

function getNotificationIcon(type) {
  const icons = {
    success: 'check-circle',
    error: 'exclamation-circle',
    warning: 'exclamation-triangle',
    info: 'info-circle'
  };
  return icons[type] || icons.info;
}

function getNotificationColor(type) {
  const colors = {
    success: '#10b981',
    error: '#ef4444',
    warning: '#f59e0b',
    info: '#3b82f6'
  };
  return colors[type] || colors.info;
}

// ===== Scroll Animations =====
function setupAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);
  
  // Observe all elements with fade-in class
  document.querySelectorAll('.fade-in').forEach(el => {
    el.classList.remove('visible'); // Reset visibility
    observer.observe(el);
  });
}

// ===== Tooltips =====
function setupTooltips() {
  // Initialize Bootstrap tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

// ===== Navbar Scroll Effect =====
function setupNavbarScroll() {
  const navbar = document.querySelector('.navbar');
  
  window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });
}

// ===== Lazy Loading =====
function setupLazyLoading() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });
    
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
}

// ===== Performance Optimization =====
// Debounce function for scroll events
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// ===== Error Handling =====
window.addEventListener('error', function(e) {
  console.error('JavaScript Error:', e.error);
  // You can add error reporting here
});

// ===== Scroll to Top Button =====
function setupScrollToTop() {
  // Create scroll to top button
  const scrollBtn = document.createElement('button');
  scrollBtn.className = 'scroll-to-top';
  scrollBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
  scrollBtn.setAttribute('aria-label', 'العودة إلى الأعلى');
  document.body.appendChild(scrollBtn);

  // Show/hide button based on scroll position
  window.addEventListener('scroll', debounce(() => {
    if (window.scrollY > 300) {
      scrollBtn.classList.add('visible');
    } else {
      scrollBtn.classList.remove('visible');
    }
  }, 100));

  // Scroll to top on click
  scrollBtn.addEventListener('click', () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });
}

// ===== Enhanced Form Validation =====
function setupEnhancedValidation() {
  // Real-time email validation
  const emailInputs = document.querySelectorAll('input[type="email"]');
  emailInputs.forEach(input => {
    input.addEventListener('input', function() {
      const isValid = isValidEmail(this.value);
      this.classList.toggle('is-valid', isValid && this.value.length > 0);
      this.classList.toggle('is-invalid', !isValid && this.value.length > 0);
    });
  });

  // Real-time text validation
  const textInputs = document.querySelectorAll('input[type="text"], textarea');
  textInputs.forEach(input => {
    input.addEventListener('input', function() {
      const isValid = this.value.trim().length >= 2;
      this.classList.toggle('is-valid', isValid);
      this.classList.toggle('is-invalid', !isValid && this.value.length > 0);
    });
  });
}

// ===== Performance Monitoring =====
function setupPerformanceMonitoring() {
  // Monitor page load performance
  window.addEventListener('load', () => {
    if ('performance' in window) {
      const perfData = performance.getEntriesByType('navigation')[0];
      console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');

      // Report to analytics (if needed)
      // analytics.track('page_load_time', perfData.loadEventEnd - perfData.loadEventStart);
    }
  });

  // Monitor largest contentful paint
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
    });
    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  }
}

// ===== Enhanced Accessibility =====
function setupAccessibility() {
  // Skip to main content link
  const skipLink = document.createElement('a');
  skipLink.href = '#main-content';
  skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
  skipLink.className = 'skip-link';
  skipLink.style.cssText = `
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
    transition: top 0.3s;
  `;

  skipLink.addEventListener('focus', () => {
    skipLink.style.top = '6px';
  });

  skipLink.addEventListener('blur', () => {
    skipLink.style.top = '-40px';
  });

  document.body.insertBefore(skipLink, document.body.firstChild);

  // Add main content ID
  const heroSection = document.getElementById('home');
  if (heroSection) {
    heroSection.id = 'main-content';
    heroSection.setAttribute('tabindex', '-1');
  }

  // Enhance keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      document.body.classList.add('keyboard-navigation');
    }
  });

  document.addEventListener('mousedown', () => {
    document.body.classList.remove('keyboard-navigation');
  });
}

// ===== Cookie Consent (Optional) =====
function setupCookieConsent() {
  // Check if consent already given
  if (localStorage.getItem('cookieConsent') === 'true') {
    return;
  }

  // Create cookie banner
  const banner = document.createElement('div');
  banner.className = 'cookie-banner';
  banner.innerHTML = `
    <div class="cookie-content">
      <p>نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا.</p>
      <div class="cookie-buttons">
        <button class="btn btn-primary btn-sm" onclick="acceptCookies()">موافق</button>
        <button class="btn btn-outline-secondary btn-sm" onclick="declineCookies()">رفض</button>
      </div>
    </div>
  `;

  banner.style.cssText = `
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 1rem;
    z-index: 9999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  `;

  document.body.appendChild(banner);

  // Show banner after delay
  setTimeout(() => {
    banner.style.transform = 'translateY(0)';
  }, 2000);

  // Global functions for cookie consent
  window.acceptCookies = () => {
    localStorage.setItem('cookieConsent', 'true');
    banner.style.transform = 'translateY(100%)';
    setTimeout(() => banner.remove(), 300);
  };

  window.declineCookies = () => {
    localStorage.setItem('cookieConsent', 'false');
    banner.style.transform = 'translateY(100%)';
    setTimeout(() => banner.remove(), 300);
  };
}

// ===== Update Initialize Function =====
function initializeApp() {
  setupLanguageToggle();
  setupSmoothScrolling();
  setupFormValidation();
  setupEnhancedValidation();
  setupAnimations();
  setupTooltips();
  setupNavbarScroll();
  setupLazyLoading();
  setupScrollToTop();
  setupPerformanceMonitoring();
  setupAccessibility();
  // setupCookieConsent(); // Uncomment if needed
}

// ===== Export for testing (if needed) =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    isValidEmail,
    toggleLanguage,
    sendToAPI,
    setupAccessibility,
    setupPerformanceMonitoring
  };
}
